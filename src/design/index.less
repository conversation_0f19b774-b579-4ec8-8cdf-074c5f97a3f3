@import 'color.less';
@import 'transition/index.less';
@import 'var/index.less';
@import 'public.less';
@import 'ant/index.less';
@import 'theme.less';
@import 'entry.css';

input:-webkit-autofill {
  box-shadow: 0 0 0 1000px transparent inset;
  -webkit-text-fill-color: @text-color-base;
}

:-webkit-autofill {
  transition: background-color 5000s ease-in-out 0s !important;
}

html {
  overflow: hidden;
  text-size-adjust: 100%;
}

html,
body {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  &.color-weak {
    filter: invert(80%);
  }

  &.gray-mode {
    filter: grayscale(100%);
    filter: progid:dximagetransform.microsoft.basicimage(grayscale=1);
  }
}

a:focus,
a:active,
button,
div,
svg,
span {
  outline: none;
}

// 保持 和 windi 一样的全局样式，减少升级带来的影响
ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.app-marker {
  &::before {
    content: '▌';
    position: relative;
    top: -0.07em;
    transform: scaleX(70%) scaleY(90%);
    color: rgba(var(--color-primary-500), 1);
    font-size: 1em;
  }
}

.ant-table {
  .ant-table-header {
    tr > th {
      background-color: #e4e9f2 !important;

      &::before {
        background-color: transparent !important;
      }
    }
  }
}

.ant-tag-processing {
  background-color: rgba(var(--color-primary-50), 0.5);
}

.ant-descriptions {
  .ant-descriptions-item-label {
    color: #737688 !important;
  }
}
