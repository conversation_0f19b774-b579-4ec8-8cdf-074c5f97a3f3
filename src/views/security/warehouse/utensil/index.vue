<template>
  <PageWrapper dense>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Space>
          <a-button @click="handleAdd">新增工具</a-button>
          <a-button
            type="primary"
            danger
            @click="multipleRemove(warehouseTaskUtensilListRemove)"
            :disabled="!selected"
          >
            删除
          </a-button>
          <a-button
            @click="downloadExcel(warehouseTaskUtensilListExport, '工具清单', getForm().getFieldsValue())"
          >
            导出
          </a-button>
        </Space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              {
                label: '查看',
                icon: IconEnum.PREVIEW,
                type: 'primary',
                ghost: true,
                onClick: handleInfo.bind(null, record),
              },
              {
                label: '编辑',
                icon: IconEnum.EDIT,
                type: 'primary',
                ghost: true,
                onClick: handleEdit.bind(null, record),
              },
              {
                label: '删除',
                icon: IconEnum.DELETE,
                type: 'primary',
                danger: true,
                ghost: true,
                popConfirm: {
                  placement: 'left',
                  title: `是否确认删除?`,
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <UtensilModal @register="registerModal" @reload="reload" />
    <UtensilInfoModal @register="registerInfoModal" />
  </PageWrapper>
</template>

<script setup lang="ts">
  import { PageWrapper } from '@/components/Page';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { Space } from 'ant-design-vue';
  import { 
    warehouseTaskUtensilListList, 
    warehouseTaskUtensilListExport, 
    warehouseTaskUtensilListRemove 
  } from '@/api/security/warehouse';
  import UtensilModal from './Modal.vue';
  import UtensilInfoModal from './InfoModal.vue';
  import { useModal } from '@/components/Modal';
  import { downloadExcel } from '@/utils/file/download';
  import { utensilListFormSchemas, utensilListColumns } from '../data';
  import { IconEnum } from '@/enums/appEnum';

  defineOptions({ name: 'UtensilList' });

  const [registerTable, { reload, multipleRemove, selected, getForm }] = useTable({
    rowSelection: {
      type: 'checkbox',
    },
    title: '工具清单管理',
    showIndexColumn: false,
    api: warehouseTaskUtensilListList,
    rowKey: 'id',
    useSearchForm: true,
    formConfig: {
      schemas: utensilListFormSchemas,
      labelWidth: 100,
      name: 'utensilList',
      baseColProps: {
        xs: 24,
        sm: 24,
        md: 24,
        lg: 6,
      },
    },
    columns: utensilListColumns,
    actionColumn: {
      width: 240,
      title: '操作',
      key: 'action',
      fixed: 'right',
    },
  });

  const [registerModal, { openModal }] = useModal();
  const [registerInfoModal, { openModal: openInfoModal }] = useModal();

  function handleEdit(record: Recordable) {
    openModal(true, { record, update: true });
  }

  function handleAdd() {
    openModal(true, { update: false });
  }

  async function handleDelete(record: Recordable) {
    const { id } = record;
    await warehouseTaskUtensilListRemove([id]);
    await reload();
  }

  function handleInfo(record: Recordable) {
    const { id } = record;
    openInfoModal(true, id);
  }
</script>

<style scoped></style>
