<template>
  <BasicModal
    v-bind="$attrs"
    :title="modalTitle"
    @register="registerInnerModal"
    :footer="null"
    :width="1400"
    :min-height="600"
  >
    <div class="utensil-list-container">
      <!-- 顶部信息区域 -->
      <div class="top-info-section">
        <!-- 任务基本信息 -->
        <div class="task-info-card">
          <div class="card-header">
            <div class="header-line"></div>
            <h3 class="card-title">任务基本信息</h3>
          </div>
          <div class="task-info-grid">
            <div class="info-item">
              <span class="info-label">任务编号</span>
              <span class="info-value">{{ currentTask?.taskNum || 'W202505190001' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">施工计划编号</span>
              <span class="info-value">{{ currentTask?.planNum || 'W202505190001' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">站点名称</span>
              <span class="info-value">{{ currentTask?.stationName || '测试站点1' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">任务状态</span>
              <a-tag :color="getTaskStatusColor(currentTask?.status)" class="status-tag">
                {{ getTaskStatusText(currentTask?.status) }}
              </a-tag>
            </div>
            <div class="info-item">
              <span class="info-label">执行人名称</span>
              <span class="info-value">{{ currentTask?.userName || '张三' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">工具种类</span>
              <span class="info-value">{{ toolTypeCount || 'XXXX' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">工具数量</span>
              <span class="info-value">{{ totalToolCount || '4' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">创建时间</span>
              <span class="info-value">{{
                formatDateTime(currentTask?.createTime) || '2025-05-19 08:30'
              }}</span>
            </div>
          </div>
        </div>

        <!-- 出入库统计 -->
        <div class="statistics-card">
          <div class="card-header">
            <div class="header-line"></div>
            <h3 class="card-title">出入库统计</h3>
          </div>
          <div class="statistics-grid">
            <div class="stat-item out-stat">
              <div class="stat-icon">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M7 17L17 7M17 7H7M17 7V17"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
              <div class="stat-content">
                <div class="stat-label">领用数量</div>
                <div class="stat-value"
                  >{{ outCount || '85' }} <span class="stat-unit">个</span></div
                >
              </div>
              <div class="stat-visual">
                <div class="visual-box out-box">
                  <div class="box-grid">
                    <div class="grid-item" v-for="i in 9" :key="i"></div>
                  </div>
                </div>
              </div>
            </div>

            <div class="stat-item in-stat">
              <div class="stat-icon">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M17 7L7 17M7 17H17M7 17V7"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
              <div class="stat-content">
                <div class="stat-label">归还数量</div>
                <div class="stat-value"
                  >{{ inCount || '50' }} <span class="stat-unit">个</span></div
                >
              </div>
              <div class="stat-visual">
                <div class="visual-box in-box">
                  <div class="box-grid">
                    <div class="grid-item" v-for="i in 9" :key="i"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 工具清单表格 -->
      <div class="table-section">
        <div class="table-header">
          <div class="header-line"></div>
          <h3 class="table-title">出入库工具列表</h3>
        </div>

        <BasicTable @register="registerTable" class="utensil-table">
          <!-- <template #toolbar>
            <div class="table-toolbar">
              <Space>
                <a-button type="primary" @click="handleAddUtensil" class="action-btn">
                  <template #icon>
                    <PlusOutlined />
                  </template>
                  新增工具
                </a-button>
                <a-button
                  type="primary"
                  danger
                  @click="multipleRemove(warehouseTaskUtensilListRemove)"
                  :disabled="!selected"
                  class="action-btn"
                >
                  <template #icon>
                    <DeleteOutlined />
                  </template>
                  批量删除
                </a-button>
                <a-button
                  @click="
                    downloadExcel(
                      warehouseTaskUtensilListExport,
                      '工具清单',
                      getUtensilListParams(),
                    )
                  "
                  class="action-btn"
                >
                  <template #icon>
                    <ExportOutlined />
                  </template>
                  导出Excel
                </a-button>
              </Space>
            </div>
          </template> -->

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'utensilName'">
              <span class="tool-name">{{ record.utensilName }}</span>
            </template>

            <template v-if="column.key === 'utensilLocation'">
              <span class="tool-location">{{ record.utensilLocation || 'XXXXXX' }}</span>
            </template>

            <template v-if="column.key === 'warehouseCount'">
              <span class="count-value">{{ record.warehouseCount || '4' }}</span>
            </template>

            <template v-if="column.key === 'receiveCount'">
              <span class="count-value">{{ record.receiveCount || '4' }}</span>
            </template>

            <template v-if="column.key === 'returnCount'">
              <span class="count-value return-count" :class="getReturnCountClass(record)">
                {{ getReturnCountDisplay(record) }}
              </span>
            </template>

            <template v-if="column.key === 'action'">
              <a-button
                type="link"
                size="small"
                @click="handleViewUtensil(record)"
                class="view-btn"
              >
                查看
              </a-button>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>

    <!-- 工具编辑弹窗 -->
    <UtensilModal @register="registerUtensilModal" @reload="reloadUtensilList" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner, useModal } from '@/components/Modal';
  import { BasicTable, useTable } from '@/components/Table';
  import { Space, Tag as ATag } from 'ant-design-vue';
  import { PlusOutlined, DeleteOutlined, ExportOutlined } from '@ant-design/icons-vue';
  import {
    warehouseTaskUtensilListList,
    warehouseTaskUtensilListRemove,
    warehouseTaskUtensilListExport,
  } from '@/api/security/warehouse';
  import UtensilModal from './UtensilModal.vue';
  import { downloadExcel } from '@/utils/file/download';
  import { utensilListFormSchemas, optimizedUtensilListColumns } from './data';
  import { taskStatusMap, taskStatusColorMap } from './dict';
  import { ref, computed } from 'vue';
  import { formatToDateTime } from '@/utils/dateUtil';

  defineOptions({ name: 'UtensilListModal' });

  const currentTask = ref<Recordable>({});
  const utensilList = ref<Recordable[]>([]);

  const modalTitle = computed(() => `工具清单管理 - ${currentTask.value?.taskNum || ''}`);

  // 统计数据
  const toolTypeCount = computed(() => {
    return utensilList.value.length || 0;
  });

  const totalToolCount = computed(() => {
    return utensilList.value.reduce((total, item) => total + (item.utensilCount || 0), 0);
  });

  const outCount = computed(() => {
    return utensilList.value.reduce((total, item) => total + (item.receiveCount || 0), 0);
  });

  const inCount = computed(() => {
    return utensilList.value.reduce((total, item) => total + (item.returnCount || 0), 0);
  });

  const [registerTable, { reload: reloadUtensilList, multipleRemove, selected, getForm }] =
    useTable({
      inset: true,
      showIndexColumn: false,
      showTableSetting: false,
      scroll: {
        y: 400,
      },

      api: async (params) => {
        const result = await warehouseTaskUtensilListList({
          ...params,
          taskId: currentTask.value?.id,
        });
        utensilList.value = result.rows || [];
        return result;
      },
      rowKey: 'id',
      useSearchForm: false,
      formConfig: {
        schemas: utensilListFormSchemas,
        labelWidth: 100,
        name: 'utensilList',
        baseColProps: {
          xs: 24,
          sm: 24,
          md: 24,
          lg: 8,
        },
      },
      columns: optimizedUtensilListColumns,
      actionColumn: {
        width: 80,
        title: '操作',
        key: 'action',
        fixed: 'right',
      },
    });

  const [registerUtensilModal, { openModal: openUtensilModal }] = useModal();

  const [registerInnerModal, { closeModal }] = useModalInner(async (task: Recordable) => {
    if (!task) {
      return closeModal();
    }

    currentTask.value = task;
    await reloadUtensilList();
  });

  // 状态相关方法
  function getTaskStatusColor(status: string) {
    return taskStatusColorMap[status] || 'default';
  }

  function getTaskStatusText(status: string) {
    return taskStatusMap[status] || status || '待领取';
  }

  // 时间格式化
  function formatDateTime(dateTime: string | Date) {
    if (!dateTime) return '';
    return formatToDateTime(dateTime);
  }

  // 归还数量显示逻辑
  function getReturnCountDisplay(record: Recordable) {
    const returnCount = record.returnCount;
    const receiveCount = record.receiveCount || 0;

    if (returnCount === undefined || returnCount === null) {
      return '未归还';
    }

    return returnCount;
  }

  function getReturnCountClass(record: Recordable) {
    const returnCount = record.returnCount;
    const receiveCount = record.receiveCount || 0;

    if (returnCount === undefined || returnCount === null) {
      return 'not-returned';
    }

    if (returnCount < receiveCount) {
      return 'partial-returned';
    }

    return 'fully-returned';
  }

  // 工具操作方法
  function handleAddUtensil() {
    openUtensilModal(true, {
      update: false,
      taskId: currentTask.value?.id,
    });
  }

  function handleEditUtensil(record: Recordable) {
    openUtensilModal(true, {
      record,
      update: true,
      taskId: currentTask.value?.id,
    });
  }

  function handleViewUtensil(record: Recordable) {
    openUtensilModal(true, {
      record,
      update: false,
      taskId: currentTask.value?.id,
      viewOnly: true,
    });
  }

  async function handleDeleteUtensil(record: Recordable) {
    const { id } = record;
    await warehouseTaskUtensilListRemove([id]);
    await reloadUtensilList();
  }

  function getUtensilListParams() {
    return {
      ...getForm().getFieldsValue(),
      taskId: currentTask.value?.id,
    };
  }
</script>

<style scoped>
  /* 响应式适配 */
  @media (max-width: 1200px) {
    .top-info-section {
      flex-direction: column;
    }

    .task-info-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 768px) {
    .task-info-grid {
      grid-template-columns: 1fr;
    }

    .statistics-grid {
      gap: 16px;
    }

    .stat-item {
      padding: 12px;
    }

    .stat-value {
      font-size: 20px;
    }

    .visual-box {
      width: 48px;
      height: 48px;
    }
  }

  .utensil-list-container {
    padding: 0;
  }

  /* 顶部信息区域 */
  .top-info-section {
    display: flex;
    gap: 24px;
    margin-bottom: 24px;
  }

  /* 卡片通用样式 */
  .task-info-card,
  .statistics-card {
    overflow: hidden;
    background: #f5f7fa;
  }

  .task-info-card {
    flex: 2;
  }

  .statistics-card {
    flex: 1;
    min-width: 320px;
  }

  /* 卡片头部 */
  .card-header {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background: #f5f7fa;
  }

  .header-line {
    width: 4px;
    height: 16px;
    margin-right: 12px;
    border-radius: 2px;
    background: #1890ff;
  }

  .card-title {
    margin: 0;
    color: #262626;
    font-size: 16px;
    font-weight: 600;
  }

  /* 任务信息网格 */
  .task-info-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px 24px;
    padding: 20px;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .info-label {
    color: #8c8c8c;
    font-size: 12px;
    line-height: 1.5;
  }

  .info-value {
    color: #262626;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
  }

  .status-tag {
    align-self: flex-start;
    margin: 0;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
  }

  /* 统计区域 */
  .statistics-grid {
    display: flex;
    flex-direction: column;
    padding: 20px;
    gap: 24px;
  }

  .stat-item {
    display: flex;
    position: relative;
    align-items: center;
    padding: 16px;
    border-radius: 8px;
    gap: 12px;
  }

  .out-stat {
    border: 1px solid #ffebe0;
    background: linear-gradient(135deg, #fff2e8 0%, #fff7f0 100%);
  }

  .in-stat {
    border: 1px solid #d1f2ff;
    background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  }

  .stat-icon {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
  }

  .out-stat .stat-icon {
    background: #ff7a45;
    color: white;
  }

  .in-stat .stat-icon {
    background: #1890ff;
    color: white;
  }

  .stat-icon svg {
    width: 14px;
    height: 14px;
  }

  .stat-content {
    flex: 1;
  }

  .stat-label {
    margin-bottom: 4px;
    color: #8c8c8c;
    font-size: 12px;
  }

  .stat-value {
    color: #262626;
    font-size: 24px;
    font-weight: 600;
    line-height: 1;
  }

  .stat-unit {
    margin-left: 4px;
    color: #8c8c8c;
    font-size: 14px;
    font-weight: 400;
  }

  /* 可视化盒子 */
  .stat-visual {
    flex-shrink: 0;
  }

  .visual-box {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    padding: 8px;
    border-radius: 8px;
  }

  .out-box {
    border: 1px solid rgb(255 122 69 / 20%);
    background: rgb(255 122 69 / 10%);
  }

  .in-box {
    border: 1px solid rgb(24 144 255 / 20%);
    background: rgb(24 144 255 / 10%);
  }

  .box-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2px;
    width: 100%;
    height: 100%;
  }

  .grid-item {
    border-radius: 1px;
  }

  .out-box .grid-item {
    background: rgb(255 122 69 / 30%);
  }

  .in-box .grid-item {
    background: rgb(24 144 255 / 30%);
  }

  /* 表格区域 */
  .table-section {
    overflow: hidden;
  }

  .table-header {
    display: flex;
    align-items: center;
    padding: 16px 20px;
  }

  .table-title {
    margin: 0;
    color: #262626;
    font-size: 16px;
    font-weight: 600;
  }

  .table-toolbar {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
  }

  .action-btn {
    border-radius: 6px;
    font-weight: 500;
  }

  /* 表格内容样式 */
  .utensil-table :deep(.ant-table) {
    border-radius: 0;
  }

  .utensil-table :deep(.ant-table-thead > tr > th) {
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #f5f7fa;
    color: #262626;
    font-weight: 600;
  }

  .utensil-table :deep(.ant-table-tbody > tr > td) {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .utensil-table :deep(.ant-table-tbody > tr:hover > td) {
    background: #f5f5f5;
  }

  .tool-name {
    color: #262626;
    font-weight: 500;
  }

  .tool-location {
    color: #8c8c8c;
    font-family: Monaco, Menlo, monospace;
  }

  .count-value {
    color: #262626;
    font-weight: 500;
  }

  .return-count.not-returned {
    color: #ff4d4f;
  }

  .return-count.partial-returned {
    color: #faad14;
  }

  .return-count.fully-returned {
    color: #52c41a;
  }

  .view-btn {
    height: auto;
    padding: 0;
    color: #1890ff;
    font-size: 14px;
  }

  .view-btn:hover {
    color: #40a9ff;
  }
</style>
