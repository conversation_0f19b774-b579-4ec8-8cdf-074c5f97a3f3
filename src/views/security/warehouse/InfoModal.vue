<template>
  <BasicDrawer
    v-bind="$attrs"
    :width="1200"
    title="任务详情"
    :footer="null"
    @register="registerInnerDrawer"
  >
    <div v-if="showSkeleton" class="skeleton-container">
      <Skeleton active :paragraph="{ rows: 8 }" />
    </div>
    <div v-else class="warehouse-task-info-container">
      <!-- 顶部信息区域 -->
      <div class="top-info-section">
        <!-- 任务基本信息 -->
        <div class="task-info-card">
          <div class="card-header">
            <div class="header-line"></div>
            <h3 class="card-title">任务基本信息</h3>
          </div>
          <div class="task-info-grid">
            <div class="info-item">
              <span class="info-label">任务编号</span>
              <span class="info-value">{{ taskData?.taskNum || 'W202505190001' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">施工计划编号</span>
              <span class="info-value">{{ taskData?.constructionDayPlanNum || 'W202505190001' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">站点名称</span>
              <span class="info-value">{{ taskData?.stationName || '测试站点1' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">任务状态</span>
              <a-tag :color="getTaskStatusColor(taskData?.status)" class="status-tag">
                {{ getTaskStatusText(taskData?.status) }}
              </a-tag>
            </div>
            <div class="info-item">
              <span class="info-label">执行人名称</span>
              <span class="info-value">{{ taskData?.userName || '张三' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">工具种类</span>
              <span class="info-value">{{ toolTypeCount || 'XXXX' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">工具数量</span>
              <span class="info-value">{{ totalToolCount || '4' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">创建时间</span>
              <span class="info-value">{{ formatDateTime(taskData?.createTime) || '2025-05-19 08:30' }}</span>
            </div>
          </div>
        </div>

        <!-- 出入库统计 -->
        <div class="statistics-card flex flex-col">
          <div class="card-header flex-none">
            <div class="header-line"></div>
            <h3 class="card-title">出入库统计</h3>
          </div>
          <div class="statistics-grid !flex-1 !h-0 !overflow-hidden">
            <div class="stat-item out-stat flex-1 w-0 h-full">
              <div class="stat-content">
                <div class="stat-label">领用数量</div>
                <div class="stat-value">{{ outCount || '85' }} <span class="stat-unit">个</span></div>
              </div>
              <div class="stat-visual">
                <img :src="warehouseInputImg" alt="领用" class="stat-image" />
              </div>
            </div>

            <div class="stat-item in-stat flex-1 w-0 h-full">
              <div class="stat-content">
                <div class="stat-label">归还数量</div>
                <div class="stat-value">{{ inCount || '50' }} <span class="stat-unit">个</span></div>
              </div>
              <div class="stat-visual">
                <img :src="warehouseOutputImg" alt="归还" class="stat-image" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 工具清单表格 -->
      <div class="table-section">
        <div class="table-header">
          <div class="header-line"></div>
          <h3 class="table-title">出入库工器具列表</h3>
        </div>

        <BasicTable @register="registerTable" class="utensil-table" :style="{ '--vben-basic-table-form-container-padding': 0 }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'utensilName'">
              <span class="tool-name">{{ record.utensilName || '扳手' }}</span>
            </template>

            <template v-if="column.key === 'position'">
              <span class="tool-location">{{ record.position || 'XXXXXX' }}</span>
            </template>

            <template v-if="column.key === 'utensilType'">
              <span class="tool-type">{{ record.utensilType || '/' }}</span>
            </template>

            <template v-if="column.key === 'stockCount'">
              <span class="count-value">{{ record.stockCount || '0' }}</span>
            </template>

            <template v-if="column.key === 'utensilCount'">
              <span class="count-value">{{ record.utensilCount || '4' }}</span>
            </template>

            <template v-if="column.key === 'receiveCount'">
              <span class="count-value">{{ record.receiveCount || '4' }}</span>
            </template>

            <template v-if="column.key === 'returnCount'">
              <span class="count-value return-count" :class="getReturnCountClass(record)">
                {{ getReturnCountDisplay(record) }}
              </span>
            </template>

            <template v-if="column.key === 'action'">
              <a-button
                type="link"
                size="small"
                @click="handleViewUtensil(record)"
                class="view-btn"
              >
                查看
              </a-button>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>

    <!-- 工具详情弹窗 -->
    <UtensilInfoModal v-if="getOpen" @register="registerUtensilInfoModal" />
  </BasicDrawer>
</template>

<script setup lang="ts">
  import { BasicDrawer, useDrawerInner } from '@/components/Drawer';
  import { BasicTable, useTable } from '@/components/Table';
  import { warehouseTaskInfo, warehouseTaskUtensilListList } from '@/api/security/warehouse';
  import { Skeleton, Tag as ATag } from 'ant-design-vue';
  import { ref, computed } from 'vue';
  import { useModal } from '@/components/Modal';
  import { formatToDateTime } from '@/utils/dateUtil';
  import { taskStatusMap, taskStatusColorMap } from './dict';
  import { utensilListFormSchemas } from './data';
  import UtensilInfoModal from './utensil/InfoModal.vue';

  // 图片资源
  import warehouseInputImg from '@/assets/images/warehouse/warehouse-input.png';
  import warehouseOutputImg from '@/assets/images/warehouse/warehouse-output.png';

  defineOptions({ name: 'WarehouseTaskInfoModal' });

  const showSkeleton = ref<boolean>(true);
  const taskData = ref<Recordable>({});
  const utensilList = ref<Recordable[]>([]);

  // 统计数据
  const toolTypeCount = computed(() => {
    return utensilList.value.length || 0;
  });

  const totalToolCount = computed(() => {
    return utensilList.value.reduce((total, item) => total + (item.utensilCount || 0), 0);
  });

  const outCount = computed(() => {
    return utensilList.value.reduce((total, item) => total + (item.receiveCount || 0), 0);
  });

  const inCount = computed(() => {
    return utensilList.value.reduce((total, item) => total + (item.returnCount || 0), 0);
  });

  // 表格配置
  const columns = [
    {
      title: '工具名称',
      dataIndex: 'utensilName',
      key: 'utensilName',
      width: 120,
    },
    {
      title: '工具型号',
      dataIndex: 'utensilType',
      key: 'utensilType',
      width: 100,
    },
    {
      title: '工具位置',
      dataIndex: 'position',
      key: 'position',
      width: 120,
    },
    {
      title: '料库单工具数量',
      dataIndex: 'utensilCount',
      key: 'utensilCount',
      width: 140,
      align: 'center',
    },
    {
      title: '库存数量',
      dataIndex: 'stockCount',
      key: 'stockCount',
      width: 100,
      align: 'center',
    },
    {
      title: '领用数量',
      dataIndex: 'receiveCount',
      key: 'receiveCount',
      width: 100,
      align: 'center',
    },
    {
      title: '归还数量',
      dataIndex: 'returnCount',
      key: 'returnCount',
      width: 100,
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      align: 'center',
    },
  ];

  const [registerTable, { reload: reloadUtensilList }] = useTable({
    inset: true,
    showIndexColumn: false,
    showTableSetting: false,
    scroll: {
      y: 300,
    },
    api: async (params) => {
      // 确保 taskId 存在才调用接口
      if (!taskData.value?.id) {
        return {
          rows: [],
          total: 0,
        };
      }

      try {
        const result = await warehouseTaskUtensilListList({
          ...params,
          taskId: taskData.value.id,
        });
        utensilList.value = result.rows || [];
        return result;
      } catch (error) {
        console.error('获取工器具列表失败:', error);
        utensilList.value = [];
        return {
          rows: [],
          total: 0,
        };
      }
    },
    rowKey: 'id',
    useSearchForm: true,
    formConfig: {
      schemas: utensilListFormSchemas,
      labelWidth: 100,
      name: 'utensilListSearch',
      baseColProps: {
        xs: 24,
        sm: 24,
        md: 24,
        lg: 8,
      },
    },
    columns,
    pagination: {
      pageSize: 10,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
    },
  });

  const [registerUtensilInfoModal, { openModal: openUtensilInfoModal }] = useModal();

  const [registerInnerDrawer, { closeDrawer, getOpen }] = useDrawerInner(async (taskId: string | number) => {
    showSkeleton.value = true;
    if (!taskId) {
      return closeDrawer();
    }

    try {
      const data = await warehouseTaskInfo(taskId);
      taskData.value = data;
      await reloadUtensilList();
      showSkeleton.value = false;
    } catch (error) {
      console.error('获取任务详情失败:', error);
      showSkeleton.value = false;
    }
  });

  // 状态相关方法
  function getTaskStatusColor(status: string) {
    return taskStatusColorMap[status] || 'default';
  }

  function getTaskStatusText(status: string) {
    return taskStatusMap[status] || status || '待领取';
  }

  // 时间格式化
  function formatDateTime(dateTime: string | Date) {
    if (!dateTime) return '';
    return formatToDateTime(dateTime);
  }

  // 归还数量显示逻辑
  function getReturnCountDisplay(record: Recordable) {
    const returnCount = record.returnCount;

    if (returnCount === undefined || returnCount === null) {
      return '未归还';
    }

    return returnCount;
  }

  function getReturnCountClass(record: Recordable) {
    const returnCount = record.returnCount;
    const receiveCount = record.receiveCount || 0;

    if (returnCount === undefined || returnCount === null) {
      return 'not-returned';
    }

    if (returnCount < receiveCount) {
      return 'partial-returned';
    }

    return 'fully-returned';
  }

  // 工具操作方法
  function handleViewUtensil(record: Recordable) {
    openUtensilInfoModal(true, record.id);
  }
</script>

<style scoped>
  @media (max-width: 768px) {
    .task-info-grid {
      grid-template-columns: 1fr;
    }

    .statistics-grid {
      gap: 16px;
    }

    .stat-item {
      padding: 12px;
    }

    .stat-value {
      font-size: 20px;
    }

    .stat-image {
      width: 48px;
      height: 48px;
    }
  }

  .skeleton-container {
    padding: 24px;
  }

  .warehouse-task-info-container {
    padding: 0;
  }

  /* 顶部信息区域 */
  .top-info-section {
    display: flex;
    gap: 24px;
    margin-bottom: 24px;
  }

  /* 卡片通用样式 */
  .task-info-card,
  .statistics-card {
    overflow: hidden;
    border-radius: 12px;
    background: rgba(var(--color-primary-500), 0.04);
  }

  .task-info-card {
    flex: 1;
    width: 50%;
  }

  .statistics-card {
    flex: 1;
    width: 50%;
  }

  /* 卡片头部 */
  .card-header {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background: #f5f7fa;
  }

  .header-line {
    width: 4px;
    height: 16px;
    margin-right: 12px;
    background: rgba(var(--color-primary-500), 1);
  }

  .card-title {
    margin: 0;
    color: #262626;
    font-size: 16px;
    font-weight: 600;
  }

  /* 任务信息网格 */
  .task-info-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 16px 24px;
    padding: 20px;
  }

  .info-item {
    display: flex;
    flex-direction: row;
    gap: 4px;
  }

  .info-label {
    width: 120px;
    color: #737688;
    line-height: 1.5;
  }

  .info-value {
    color: #262626;
    font-weight: 500;
    line-height: 1.5;
  }

  .status-tag {
    align-self: flex-start;
    margin: 0;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
  }

  /* 统计区域 */
  .statistics-grid {
    display: flex;
    flex-direction: row;
    padding: 20px;
    gap: 24px;
  }

  .stat-item {
    display: flex;
    position: relative;
    flex-direction: column;
    align-items: center;
    padding: 24px 24px 0;
    border-radius: 8px;
    gap: 12px;
  }

  .out-stat {
    background-color: #FFF;
  }

  .in-stat {
    background-color: #FFF;
  }

  .stat-content {
    display: flex;
    flex-direction: column;
    align-items: start;
    width: 100%;
  }

  .stat-label {
    margin-bottom: 12px;
    color: #8c8c8c;
  }

  .stat-value {
    color: #262626;
    font-size: 36px;
    font-weight: 600;
    line-height: 1;
  }

  .stat-unit {
    margin-left: 4px;
    color: #8c8c8c;
    font-size: 14px;
    font-weight: 400;
  }

  /* 统计图片 */
  .stat-visual {
    flex-shrink: 0;
    margin-top: 42px;
  }

  .stat-image {
    width: 100%;
    object-fit: contain;
  }

  /* 表格区域 */
  .table-section {
    overflow: hidden;
  }

  .table-header {
    display: flex;
    align-items: center;
    padding-top: 12px;
    padding-bottom: 24px;
  }

  .table-title {
    margin: 0;
    color: #262626;
    font-size: 16px;
    font-weight: 600;
  }

  /* 表格内容样式 */
  .utensil-table :deep(.ant-table) {
    border-radius: 0;
  }

  .utensil-table :deep(.ant-table-thead > tr > th) {
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #f5f7fa;
    color: #262626;
    font-weight: 600;
  }

  .utensil-table :deep(.ant-table-tbody > tr > td) {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .utensil-table :deep(.ant-table-tbody > tr:hover > td) {
    background: #f5f5f5;
  }

  .tool-name {
    color: #262626;
    font-weight: 500;
  }

  .tool-location {
    color: #8c8c8c;
    font-family: Monaco, Menlo, monospace;
  }

  .tool-type {
    color: #595959;
    font-size: 13px;
  }

  .count-value {
    color: #262626;
    font-weight: 500;
  }

  .return-count.not-returned {
    color: #ff4d4f;
  }

  .return-count.partial-returned {
    color: #faad14;
  }

  .return-count.fully-returned {
    color: #52c41a;
  }

  .view-btn {
    height: auto;
    padding: 0;
    color: #1890ff;
    font-size: 14px;
  }

  .view-btn:hover {
    color: #40a9ff;
  }
</style>
