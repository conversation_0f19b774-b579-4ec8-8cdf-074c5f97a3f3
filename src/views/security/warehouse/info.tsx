import { DescItem } from '@/components/Description';
import { useRender } from '@/hooks/component/useRender';
import { DictEnum } from '@/enums/dictEnum';

const { renderDict } = useRender();

/**
 * 料库任务详情描述配置
 */
export const warehouseTaskDescSchema: DescItem[] = [
  {
    field: 'taskNum',
    label: '任务编号',
  },
  {
    field: 'constructionDayPlanNum',
    label: '施工日计划编号',
  },
  {
    field: 'stationName',
    label: '所属站点',
  },
  {
    field: 'status',
    label: '任务状态',
    render: (value) => renderDict(value, DictEnum.WAREHOUSE_TASK_STATUS),
  },
  {
    field: 'userName',
    label: '执行人',
  },
  {
    field: 'utensilTypeCount',
    label: '工具种类',
  },
  {
    field: 'utensilCount',
    label: '工具数量',
  },
  {
    field: 'issueTime',
    label: '签发时间',
  },
  {
    field: 'company',
    label: '单位',
  },
  {
    field: 'workTicketIssuer',
    label: '工作票签发人',
  },
  {
    field: 'reviewer',
    label: '审核人',
  },
  {
    field: 'workLeader',
    label: '工作领导人',
  },
  {
    field: 'stationLiaisonPerson',
    label: '驻站联络人',
  },
  {
    field: 'groundLineGuardian',
    label: '地线监护人',
  },
  {
    field: 'workTeam',
    label: '工作组员',
  },
  {
    field: 'powerOutageRange',
    label: '停电范围',
  },
  {
    field: 'scopeWork',
    label: '作业范围',
  },
  {
    field: 'workTask',
    label: '工作任务',
  },
  {
    field: 'plannedWorkingHours',
    label: '计划工作时间',
  },
  {
    field: 'reviewWorkTicketCommandNum',
    label: '审核工作票命令号',
  },
  {
    field: 'permittedOperationCommandNum',
    label: '准许作业命令号',
  },
  {
    field: 'remark',
    label: '备注',
    render: (value) => value || '无',
  },
  {
    field: 'createTime',
    label: '创建时间',
  },
];

/**
 * 工具清单详情描述配置
 */
export const utensilListDescSchema: DescItem[] = [
  {
    field: 'utensilName',
    label: '工具名称',
  },
  {
    field: 'utensilType',
    label: '工具型号',
  },
  {
    field: 'position',
    label: '工具位置',
  },
  {
    field: 'utensilCount',
    label: '工具数量',
  },
  {
    field: 'stockCount',
    label: '库存数量',
  },
  {
    field: 'receiveCount',
    label: '领取数量',
  },
  {
    field: 'returnCount',
    label: '归还数量',
  },
  {
    field: 'status',
    label: '状态',
    render: (value) => {
      if (!value) return '未设置';
      return renderDict(value, DictEnum.WAREHOUSE_UTENSIL_STATUS);
    },
  },
  {
    field: 'receiveTime',
    label: '领取时间',
    render: (value) => value || '未领取',
  },
  {
    field: 'returnTime',
    label: '归还时间',
    render: (value) => value || '未归还',
  },
  {
    field: 'remark',
    label: '备注',
    render: (value) => value || '无',
  },
];
