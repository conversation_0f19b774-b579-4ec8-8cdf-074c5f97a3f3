<template>
  <BasicModal
    v-bind="$attrs"
    :title="title"
    @register="registerInnerModal"
    @ok="handleSubmit"
    :footer="viewOnly ? null : undefined"
    :width="600"
  >
    <!-- 只读模式：使用Description组件 -->
    <template v-if="viewOnly">
      <Skeleton v-if="showSkeleton" active />
      <Description v-else @register="registerDescription" />
    </template>

    <!-- 编辑模式：使用Form组件 -->
    <BasicForm v-else @register="registerForm" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { Description, useDescription } from '@/components/Description';
  import { Skeleton, message } from 'ant-design-vue';
  import { utensilListModalSchemas } from './data';
  import { utensilListDescSchema } from './info';
  import {
    warehouseTaskUtensilListAdd,
    warehouseTaskUtensilListUpdate,
    warehouseTaskUtensilListInfo,
  } from '@/api/security/warehouse';
  import { ref, computed, unref } from 'vue';

  defineOptions({ name: 'UtensilModal' });

  const emit = defineEmits(['reload']);

  const isUpdate = ref<boolean>(false);
  const viewOnly = ref<boolean>(false);
  const showSkeleton = ref<boolean>(false);
  const record = ref<Recordable>({});
  const taskId = ref<string>('');

  const title = computed(() => {
    if (unref(viewOnly)) return '工具详情';
    return unref(isUpdate) ? '编辑工具' : '新增工具';
  });

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    schemas: utensilListModalSchemas,
    showActionButtonGroup: false,
    baseColProps: {
      xs: 24,
      sm: 24,
      md: 24,
      lg: 24,
    },
  });

  const [registerDescription, { setDescProps }] = useDescription({
    schema: utensilListDescSchema,
    column: 2,
    labelStyle: {
      width: '120px',
      minWidth: '120px',
    },
  });

  const [registerInnerModal, { closeModal }] = useModalInner(async (data) => {
    // 重置状态
    await resetFields();
    isUpdate.value = !!data?.update;
    viewOnly.value = !!data?.viewOnly;
    record.value = data?.record || {};
    taskId.value = data?.taskId || '';

    if (unref(viewOnly)) {
      // 只读模式：获取详细信息并显示
      showSkeleton.value = true;
      try {
        const response = await warehouseTaskUtensilListInfo(data.record?.id);
        setDescProps({ data: response.data });
        showSkeleton.value = false;
      } catch (error) {
        console.error('获取工具详情失败:', error);
        showSkeleton.value = false;
      }
    } else if (unref(isUpdate)) {
      // 编辑模式：设置表单值
      await setFieldsValue(data.record);
    } else {
      // 新增模式：设置默认值
      await setFieldsValue({
        taskId: taskId.value,
        status: '1', // 默认待领取状态
        utensilCount: 1,
        receiveCount: 0,
        returnCount: 0,
      });
    }
  });

  async function handleSubmit() {
    // 只读模式下不执行提交
    if (unref(viewOnly)) {
      return;
    }

    try {
      const values = await validate();

      if (unref(isUpdate)) {
        await warehouseTaskUtensilListUpdate({ ...record.value, ...values });
        message.success('编辑成功');
      } else {
        await warehouseTaskUtensilListAdd(values);
        message.success('新增成功');
      }

      closeModal();
      emit('reload');
    } catch (error) {
      console.error('提交失败:', error);
    }
  }
</script>

<style scoped></style>
