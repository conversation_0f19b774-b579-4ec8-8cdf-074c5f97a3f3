import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';
import { stationOptionSelect } from '@/api/business/station';
import { useRender } from '@/hooks/component/useRender';
import { getDictOptions } from '@/utils/dict';
import { DictEnum } from '@/enums/dictEnum';
// import {
//   taskStatusDict,
//   taskStatusMap,
//   taskStatusColorMap,
//   utensilStatusDict,
//   utensilStatusMap,
//   utensilStatusColorMap,
// } from './dict';
// import { Tag } from 'ant-design-vue';
// import { h } from 'vue';

export const { renderDict } = useRender();

// ==================== 料库任务管理 ====================

/**
 * 料库任务表格列配置
 */
export const warehouseTaskColumns: BasicColumn[] = [
  { title: '任务编号', dataIndex: 'taskNum', width: 180 },
  { title: '施工日计划编号', dataIndex: 'constructionDayPlanNum', width: 150 },
  { title: '所属站点', dataIndex: 'stationName', width: 120 },
  {
    title: '任务状态',
    dataIndex: 'status',
    width: 100,
    customRender({ value }) {
      return renderDict(value, DictEnum.WAREHOUSE_TASK_STATUS);
    },
  },
  { title: '执行人', dataIndex: 'userName', width: 100 },
  { title: '工具种类', dataIndex: 'utensilTypeCount', width: 80 },
  { title: '工具数量', dataIndex: 'utensilCount', width: 80 },
  { title: '签发时间', dataIndex: 'issueTime', width: 150 },
  { title: '单位', dataIndex: 'company', width: 120 },
  { title: '工作票签发人', dataIndex: 'workTicketIssuer', width: 120 },
  { title: '工作领导人', dataIndex: 'workLeader', width: 120 },
  { title: '备注', dataIndex: 'remark', width: 150 },
  { title: '创建时间', dataIndex: 'createTime', width: 150 },
];

/**
 * 料库任务搜索表单配置
 */
export const warehouseTaskFormSchemas: FormSchema[] = [
  {
    label: '任务编号',
    field: 'taskNum',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '施工日计划编号',
    field: 'constructionDayPlanNum',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '所属站点',
    field: 'stationId',
    component: 'ApiSelect',
    componentProps: {
      api: stationOptionSelect,
      labelField: 'stationName',
      valueField: 'stationId',
    },
    colProps: { span: 6 },
  },
  {
    label: '任务状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.WAREHOUSE_TASK_STATUS),
    },
    colProps: { span: 6 },
  },
  {
    label: '执行人',
    field: 'userName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '单位',
    field: 'company',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '签发时间',
    field: 'issueTime',
    component: 'RangePicker',
    colProps: { span: 6 },
  },
  {
    label: '创建时间',
    field: 'createTime',
    component: 'RangePicker',
    colProps: { span: 6 },
  },
];

/**
 * 料库任务编辑表单配置
 */
export const warehouseTaskModalSchemas: FormSchema[] = [
  {
    label: '任务编号',
    field: 'taskNum',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '格式：LKRW-YYYYMMDDHHmm',
    },
  },
  {
    label: '施工日计划编号',
    field: 'constructionDayPlanNum',
    component: 'Input',
    required: true,
  },
  {
    label: '所属站点',
    field: 'stationId',
    slot: 'stationId',
    defaultValue: Number(localStorage.getItem('stationId')),
    required: true,
  },
  {
    label: '所属站点名称',
    field: 'stationName',
    component: 'Input',
    show: false,
  },
  {
    label: '任务状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.WAREHOUSE_TASK_STATUS),
    },
    required: true,
  },
  {
    label: '执行人',
    field: 'userId',
    slot: 'userId',
    required: true,
  },
  {
    label: '执行人名称',
    field: 'userName',
    component: 'Input',
    show: false,
  },
  {
    label: '签发时间',
    field: 'issueTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '审核工作票命令号',
    field: 'reviewWorkTicketCommandNum',
    component: 'Input',
  },
  {
    label: '准许作业命令号',
    field: 'permittedOperationCommandNum',
    component: 'Input',
  },
  {
    label: '单位',
    field: 'company',
    component: 'Input',
  },
  {
    label: '工作票签发人',
    field: 'workTicketIssuer',
    slot: 'workTicketIssuer',
  },
  {
    label: '审核人',
    field: 'reviewer',
    slot: 'reviewer',
  },
  {
    label: '工作领导人',
    field: 'workLeader',
    slot: 'workLeader',
  },
  {
    label: '驻站联络人',
    field: 'stationLiaisonPerson',
    slot: 'stationLiaisonPerson',
  },
  {
    label: '地线监护人',
    field: 'groundLineGuardian',
    slot: 'groundLineGuardian',
  },
  {
    label: '工作组员',
    field: 'workTeam',
    slot: 'workTeam',
  },
  {
    label: '停电范围',
    field: 'powerOutageRange',
    component: 'InputTextArea',
  },
  {
    label: '作业范围',
    field: 'scopeWork',
    component: 'InputTextArea',
  },
  {
    label: '工作任务',
    field: 'workTask',
    component: 'InputTextArea',
  },
  {
    label: '计划工作时间',
    field: 'plannedWorkingHours',
    component: 'Input',
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
  },
  {
    field: 'createBy',
    label: '创建者',
    component: 'Input',
    show: false,
  },
  {
    field: 'createTime',
    label: '创建时间',
    component: 'Input',
    show: false,
  },
];

// ==================== 料库任务工具清单 ====================

/**
 * 工具清单表格列配置
 */
export const utensilListColumns: BasicColumn[] = [
  { title: '工具名称', dataIndex: 'utensilName', width: 150 },
  { title: '工具型号', dataIndex: 'utensilType', width: 120 },
  { title: '工具数量', dataIndex: 'utensilCount', width: 80 },
  { title: '领取数量', dataIndex: 'receiveCount', width: 80 },
  { title: '归还数量', dataIndex: 'returnCount', width: 80 },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender({ value }) {
      return renderDict(value, DictEnum.WAREHOUSE_UTENSIL_STATUS);
    },
  },
  { title: '领取时间', dataIndex: 'receiveTime', width: 150 },
  { title: '归还时间', dataIndex: 'returnTime', width: 150 },
  { title: '备注', dataIndex: 'remark', width: 150 },
];

/**
 * 优化后的工具清单表格列配置（用于UtensilListModal）
 */
export const optimizedUtensilListColumns: BasicColumn[] = [
  {
    title: '工具名称',
    dataIndex: 'utensilName',
    key: 'utensilName',
    width: 120,
    ellipsis: true,
  },
  {
    title: '工具位置',
    dataIndex: 'utensilLocation',
    key: 'utensilLocation',
    width: 120,
    ellipsis: true,
  },
  {
    title: '料库单工具数量',
    dataIndex: 'warehouseCount',
    key: 'warehouseCount',
    width: 120,
    align: 'center',
  },
  {
    title: '领用数量',
    dataIndex: 'receiveCount',
    key: 'receiveCount',
    width: 100,
    align: 'center',
  },
  {
    title: '归还数量',
    dataIndex: 'returnCount',
    key: 'returnCount',
    width: 100,
    align: 'center',
  },
];

/**
 * 工具清单搜索表单配置
 */
export const utensilListFormSchemas: FormSchema[] = [
  {
    label: '工具名称',
    field: 'utensilName',
    component: 'Input',
    colProps: { span: 6 },
  },
  // {
  //   label: '工具型号',
  //   field: 'utensilType',
  //   component: 'Input',
  //   colProps: { span: 6 },
  // },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.WAREHOUSE_UTENSIL_STATUS),
    },
    colProps: { span: 6 },
  },
];

/**
 * 工具清单编辑表单配置
 */
export const utensilListModalSchemas: FormSchema[] = [
  {
    label: '料库任务ID',
    field: 'taskId',
    component: 'Input',
    required: true,
    show: false,
  },
  {
    label: '工具ID',
    field: 'utensilId',
    component: 'Input',
    required: true,
  },
  {
    label: '工具名称',
    field: 'utensilName',
    component: 'Input',
    required: true,
  },
  {
    label: '工具型号',
    field: 'utensilType',
    component: 'Input',
  },
  {
    label: '工具数量',
    field: 'utensilCount',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 1,
    },
  },
  {
    label: '领取数量',
    field: 'receiveCount',
    component: 'InputNumber',
    componentProps: {
      min: 0,
    },
  },
  {
    label: '归还数量',
    field: 'returnCount',
    component: 'InputNumber',
    componentProps: {
      min: 0,
    },
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.WAREHOUSE_UTENSIL_STATUS),
    },
    required: true,
  },
  {
    label: '领取时间',
    field: 'receiveTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '归还时间',
    field: 'returnTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
  },
];
