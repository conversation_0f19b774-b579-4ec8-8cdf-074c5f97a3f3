export enum DictEnum {
  NORMAL_DISABLE = 'sys_normal_disable',
  COMMON_STATUS = 'sys_common_status',
  JOB_GROUP = 'sys_job_group', // 定时任务分组
  JOB_STATUS = 'sys_job_status', // 任务状态
  YES_NO = 'sys_yes_no', // 是否
  SYS_USER_SEX = 'sys_user_sex', // 性别
  SHOW_HIDE = 'sys_show_hide', // 显示状态
  NOTICE_TYPE = 'sys_notice_type', // 通知类型
  NOTICE_STATUS = 'sys_notice_status', // 通知状态
  OSS_ACCESS_POLICY = 'oss_access_policy', // oss权限桶类型
  SYS_OPER_TYPE = 'sys_oper_type', // 操作类型
  SYS_DEVICE_TYPE = 'sys_device_type', // 设备类型
  SYS_GRANT_TYPE = 'sys_grant_type', // 授权类型
  WF_BUSINESS_STATUS = 'wf_business_status', // 业务状态
  WF_FORM_TYPE = 'wf_form_type', // 表单类型

  WAREHOUSE_TYPE = 'warehouse_type',
  WAREHOUSE_STATUS = 'warehouse_status',
  DEVICE_TYPE_STATUS = 'device_type_status',
  SMOKE_STATUS = 'smoke_status',
  DIRECTION = 'direction',
  UTENSIL_ACTION = 'utensil_action',
  VEHICLE_ACTION = 'vehicle_action',
  VEHICLE_TYPE = 'vehicle_type',
  VEHICLE_STATUS = 'vehicle_status',
  CONNECT_TYPE = 'connect_type',
  IS_NEED_CLEAN = 'is_need_clean',
  IS_NEED_DETECTION = 'is_need_detection',
  UTENSIL_STATUS = 'utensil_status',
  REAL_STATUS = 'real_status',
  PLACE_IS_ABNORMAL = 'place_is_abnormal',
  STORE_TYPE = 'store_type',
  STORE_ORDER_PROGRESS = 'store_order_progress',
  SCRAP_ORDER_STATUS = 'scrap_order_status',
  SCRAP_ORDER_PROGRESS = 'scrap_order_progress',
  TRANSFER_ORDER_STATUS = 'transfer_order_status',
  TRANSFER_ORDER_PROGRESS = 'transfer_order_progress',
  IS_NORMAL = 'is_normal',
  WORK_ORDER_STATUS = 'work_order_status',
  WORK_ORDER_CABINET_STATUS = 'work_order_cabinet_status',
  WORK_ORDER_TYPE = 'work_order_type',
  TASK_TYPE = 'event_task_type',
  DEVICE_STATUS = 'device_status',
  HAS_IDENTIFICATION_CODE = 'has_identification_code',
  USER_ROLE = 'user_role',
  PERSONNEL_TYPE = 'personnel_type', // 人员类型
  QUALIFICATION_INFORMATION = 'qualification_information', // 资质信息
  AR_DEVICE_STATUS = 'ar_device_status', // AR设备状态
  VEHICLE_ITEM_STATUS = 'vehicle_item_status', // 检修项状态
  VEHICLE_ITEM_TYPES = 'vehicle_item_types', // 目标检测类别
  VEHICLE_ITEM_COMPLETED = 'vehicle_item_completed', // 检修项完成状态
  TASK_STATUS = 'task_status', // 任务状态
  WORKFLOW_STATUS = 'workflow_status', // 工作流状态
  STEP_ANNEX_TYPE = 'step_annex_type', // 环节附件类型
  STEP_ANNEX_MULTIPLE_TYPE = 'step_annex_multiple_type', // 环节附件多个类型
  STEP_DETECTION_ITEM = 'step_detection_item', // 环节识别项
  RAILWAY_TASK_STATUS = 'railway_task_status', // 作业管控任务状态
  WAREHOUSE_TASK_STATUS = 'warehouse_task_status', // 料库任务状态
  WAREHOUSE_UTENSIL_STATUS = 'warehouse_utensil_status', // 料库工具状态
  EVENT_TYPE = 'event_type', // 事件类型
  EVENT_LEVEL = 'event_level', // 事件级别
  EVENT_STATUS = 'event_status', // 事件状态
}

// 提供给下拉框组件使用
export const fieldNames = { label: 'dictLabel', value: 'dictValue' };
